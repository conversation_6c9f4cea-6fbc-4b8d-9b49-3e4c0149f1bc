"use client"

import { useEffect, useState } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { useToast } from "@/components/ui/use-toast"
import { AppHeader } from "@/components/app-header"
import { AppSidebar } from "@/components/app-sidebar"
import { PageLoading } from "@/components/page-loading"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Skeleton } from "@/components/ui/skeleton"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { HelpCircle } from "lucide-react"
import { getBestAvatar, getInitials } from "@/lib/utils/avatar-utils"
import { UserService } from "@/lib/domains/user/user.service"
import { User } from "@/lib/domains/user/user.types"
import { SquadService } from "@/lib/domains/squad/squad.service"
import { SquadMember } from "@/lib/domains/squad/squad.types"

export default function SquadsPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { squads, loading, error } = useRealtimeUserSquads()
  const [squadLeaders, setSquadLeaders] = useState<Record<string, User>>({})
  const [squadMembers, setSquadMembers] = useState<Record<string, User[]>>({})
  const [loadingMembers, setLoadingMembers] = useState<Record<string, boolean>>({})

  // Fetch squad leader and member details
  useEffect(() => {
    const fetchSquadData = async () => {
      const newLeaders: Record<string, User> = {}
      const newMembers: Record<string, User[]> = {}
      const newLoadingStates: Record<string, boolean> = {}

      for (const squad of squads) {
        // Set loading state for this squad
        if (!squadMembers[squad.id] && !loadingMembers[squad.id]) {
          newLoadingStates[squad.id] = true
        }

        // Fetch leader if not already cached
        if (squad.leaderId && !squadLeaders[squad.leaderId]) {
          try {
            const leader = await UserService.getUser(squad.leaderId)
            if (leader) {
              newLeaders[squad.leaderId] = leader
            }
          } catch (error) {
            console.error(`Error fetching leader for squad ${squad.id}:`, error)
          }
        }

        // Fetch squad members if not already cached
        if (!squadMembers[squad.id] && !loadingMembers[squad.id]) {
          try {
            const squadMembersList = await SquadService.getSquadMembers(squad.id)
            const memberUsers: User[] = []

            for (const squadMember of squadMembersList) {
              // Skip the leader as we already have them
              if (squadMember.userId === squad.leaderId) continue

              try {
                const user = await UserService.getUser(squadMember.userId)
                if (user) {
                  memberUsers.push(user)
                }
              } catch (error) {
                console.error(
                  `Error fetching member ${squadMember.userId} for squad ${squad.id}:`,
                  error
                )
              }
            }

            newMembers[squad.id] = memberUsers
            newLoadingStates[squad.id] = false
          } catch (error) {
            console.error(`Error fetching members for squad ${squad.id}:`, error)
            newMembers[squad.id] = []
            newLoadingStates[squad.id] = false
          }
        }
      }

      // Update state if we have new data
      if (Object.keys(newLeaders).length > 0) {
        setSquadLeaders((prev) => ({ ...prev, ...newLeaders }))
      }
      if (Object.keys(newMembers).length > 0) {
        setSquadMembers((prev) => ({ ...prev, ...newMembers }))
      }
      if (Object.keys(newLoadingStates).length > 0) {
        setLoadingMembers((prev) => ({ ...prev, ...newLoadingStates }))
      }
    }

    if (squads.length > 0) {
      fetchSquadData()
    }
  }, [squads, squadLeaders, squadMembers, loadingMembers])

  // Show error toast if there's an error from the realtime hook
  useEffect(() => {
    if (error) {
      console.error("Error fetching squads:", error)
      toast({
        title: "Error",
        description: "Failed to load squads. Please try again.",
        variant: "destructive",
      })
    }
  }, [error, toast])

  if (loading) {
    return <PageLoading message="Loading squads..." />
  }

  return (
    <div className="min-h-screen flex flex-col">
      <AppHeader />
      <div className="flex-1 flex">
        <AppSidebar />
        <main className="flex-1 p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl font-bold">My Squads</h1>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-6 w-6"
                    aria-label="Squad information"
                  >
                    <HelpCircle className="h-4 w-4" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-3" align="start">
                  <p className="text-sm">
                    Squads aren't just for friend groups, add your partner, family, or travel
                    buddies and make every trip easier to plan.
                  </p>
                </PopoverContent>
              </Popover>
            </div>
            <Button onClick={() => router.push("/squads/create")}>Create Squad</Button>
          </div>

          {squads.length === 0 ? (
            <div className="text-center py-12">
              <h2 className="text-2xl font-semibold mb-4">No squads yet</h2>
              <p className="text-muted-foreground mb-6">
                Create your first squad to start planning trips with your friends
              </p>
              <Button onClick={() => router.push("/squads/create")}>Create Squad</Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {squads.map((squad) => (
                <Card key={squad.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <CardTitle>{squad.name}</CardTitle>
                    <CardDescription>{squad.memberCount || 0} members</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex -space-x-2 overflow-hidden mb-4">
                      {/* Show squad leader first */}
                      {squad.leaderId && squadLeaders[squad.leaderId] ? (
                        <Avatar className="border-2 border-background">
                          <AvatarImage
                            src={getBestAvatar(
                              squadLeaders[squad.leaderId].photoURL,
                              squadLeaders[squad.leaderId].displayName,
                              40
                            )}
                            alt={squadLeaders[squad.leaderId].displayName || "Squad Leader"}
                          />
                          <AvatarFallback>
                            {getInitials(squadLeaders[squad.leaderId].displayName)}
                          </AvatarFallback>
                        </Avatar>
                      ) : squad.leaderId ? (
                        <Skeleton className="h-10 w-10 rounded-full border-2 border-background" />
                      ) : null}

                      {/* Show actual member avatars or loading skeletons */}
                      {loadingMembers[squad.id] ? (
                        // Show skeleton avatars while loading
                        [...Array(Math.min(3, Math.max(0, (squad.memberCount || 0) - 1)))].map(
                          (_, i) => (
                            <Skeleton
                              key={`skeleton-${i}`}
                              className="h-10 w-10 rounded-full border-2 border-background"
                            />
                          )
                        )
                      ) : (
                        <>
                          {/* Show actual member avatars */}
                          {squadMembers[squad.id]?.slice(0, 3).map((member, i) => (
                            <Avatar key={member.id || i} className="border-2 border-background">
                              <AvatarImage
                                src={getBestAvatar(member.photoURL, member.displayName, 40)}
                                alt={member.displayName || "Squad Member"}
                              />
                              <AvatarFallback>{getInitials(member.displayName)}</AvatarFallback>
                            </Avatar>
                          ))}
                          {/* Show placeholder avatars for remaining members if we don't have their data yet */}
                          {!squadMembers[squad.id] &&
                            [...Array(Math.min(3, Math.max(0, (squad.memberCount || 0) - 1)))].map(
                              (_, i) => (
                                <Avatar
                                  key={`placeholder-${i}`}
                                  className="border-2 border-background"
                                >
                                  <AvatarFallback className="bg-muted text-muted-foreground">
                                    {i + 2}
                                  </AvatarFallback>
                                </Avatar>
                              )
                            )}
                        </>
                      )}

                      {/* Show +X indicator for additional members */}
                      {(squad.memberCount || 0) > 4 && (
                        <div className="flex items-center justify-center w-10 h-10 rounded-full border-2 border-background bg-muted text-muted-foreground text-xs">
                          +{(squad.memberCount || 0) - 4}
                        </div>
                      )}
                    </div>
                    <p className="line-clamp-3">{squad.description}</p>
                  </CardContent>
                  <CardFooter>
                    <Button asChild className="w-full">
                      <Link href={`/squads/${squad.id}`}>View Squad</Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </main>
      </div>
    </div>
  )
}
